import { useState, useEffect } from 'react';
import { getAuthCookies } from '../utils/cookies';
import { startSimpleSession } from '../services/authService';
import type { RealtimeSessionConnectionDetails } from 'gabber-client-core'; // Adjust import if needed

type AuthState = {
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  connectionDetails: RealtimeSessionConnectionDetails | null; // Use the correct type
  userId: string | undefined;
};

export const useAuth = (): AuthState => {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
    error: null,
    connectionDetails: null,
    userId: undefined,
  });

  useEffect(() => {
    const authenticate = async () => {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      const { userId, serviceToken } = getAuthCookies();

      if (!userId || !serviceToken) {
        console.error('Auth cookies not found.');
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          error: 'Session details missing. Please ensure you are logged in.', // User-friendly message
          connectionDetails: null,
          userId: undefined,
        });
        return;
      }

      try {
        console.log('Attempting to start session with token...');
        const sessionData = await startSimpleSession(serviceToken);
        console.log('Session started successfully.');

        // Ensure the structure matches what RealtimeSessionEngineProvider expects
        const connectionDetails: RealtimeSessionConnectionDetails = {
            // Map properties from sessionData to connectionDetails
            // Example - adjust based on actual StartSessionResponse structure
            url: sessionData.url, // Assuming 'url' is part of the response
            token: sessionData.token, // Assuming 'token' is part of the response
            // Add other required fields from RealtimeSessionConnectionDetails type
        };


        setAuthState({
          isLoading: false,
          isAuthenticated: true,
          error: null,
          connectionDetails: connectionDetails,
          userId: userId,
        });
      } catch (err) {
        console.error('Authentication failed:', err);
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          error: 'Session Expired or Invalid. Please try again.', // User-friendly error
          connectionDetails: null,
          userId: userId, // Keep userId if available, might be useful
        });
      }
    };

    // Run on component mount
    authenticate();

    // No cleanup needed unless setting up timers/subscriptions
  }, []); // Empty dependency array means run once on mount

  return authState;
};