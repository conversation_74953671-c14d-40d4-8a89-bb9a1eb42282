import React, { useEffect, useState } from 'react';
import { useRealtimeSessionEngine } from 'gabber-client-react';

// Example: Simple Orb that scales with Agent Volume
const VoiceVisualizer: React.FC = () => {
  const { agentState, agentVolume, connectionState } = useRealtimeSessionEngine();
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [scale, setScale] = useState(1);

  // Determine if the agent is actively speaking based on state
  useEffect(() => {
     // Adjust condition based on actual SDK states indicating speaking
     const speakingStates = ['speaking', 'thinking']; // Example states
     setIsSpeaking(speakingStates.includes(agentState));
  }, [agentState]);

  // Scale the orb based on volume when speaking
  useEffect(() => {
    if (isSpeaking && connectionState === 'connected') {
      // Map agentVolume (0-1?) to a scale factor (e.g., 1.0 to 1.5)
      const newScale = 1 + ((agentVolume ?? 0) * 0.5);
      setScale(newScale);
    } else {
      setScale(1); // Reset scale when not speaking
    }
  }, [agentVolume, isSpeaking, connectionState]);

  // Add prefers-reduced-motion check
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  return (
    <div className="flex items-center justify-center h-24 w-24">
       {/* Replace with your sophisticated waveform or use the orb */}
       <svg
         className={`w-16 h-16 text-blue-500 transition-transform duration-100 ease-linear ${prefersReducedMotion ? '' : ''}`} // Apply transition only if not reduced motion
         viewBox="0 0 100 100"
         xmlns="http://www.w3.org/2000/svg"
         style={{ transform: `scale(${prefersReducedMotion ? 1 : scale})` }} // Apply scale conditionally
         aria-hidden="true" // If purely decorative
       >
         <circle cx="50" cy="50" r="45" fill="currentColor" />
       </svg>
       {/* Add more sophisticated visualization using agentVolumeBands if needed */}
    </div>
  );
};

export default VoiceVisualizer;