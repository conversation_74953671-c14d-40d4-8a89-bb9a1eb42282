import React from 'react';
import { AlertCircle } from 'lucide-react';

interface SessionExpiredProps {
  message: string;
}

const SessionExpired: React.FC<SessionExpiredProps> = ({ message }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black p-4">
      <div className="bg-red-900 bg-opacity-20 rounded-xl p-6 max-w-md border border-red-800 backdrop-blur-sm">
        <div className="flex items-center mb-4">
          <AlertCircle className="text-red-500 mr-3 w-8 h-8" />
          <h1 className="text-xl font-semibold text-red-100">Session Error</h1>
        </div>
        
        <p className="text-red-100 mb-6">{message}</p>
        
        <button 
          className="w-full bg-red-800 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
          onClick={() => window.location.reload()}
        >
          Try Again
        </button>
      </div>
    </div>
  );
};

export default SessionExpired;