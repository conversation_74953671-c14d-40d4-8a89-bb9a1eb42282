import { RealtimeSessionEngineProvider } from 'gabber-client-react';
import { useAuth } from './hooks/useAuth';
import LoadingOrb from './components/LoadingOrb';
import SessionExpired from './components/SessionExpired';
import ImprovedChatInterface from './components/ImprovedChatInterface'; // Import the new component

function App() {
  const { isLoading, isAuthenticated, error, connectionDetails, userId } = useAuth();

  if (isLoading) return <LoadingOrb />;
  if (error) return <SessionExpired message={error} />;
  if (!isAuthenticated || !connectionDetails) {
    return <SessionExpired message="An unexpected error occurred." />;
  }

  return (
    <RealtimeSessionEngineProvider
      key={connectionDetails.token}
      connectionOpts={{ connection_details: connectionDetails }}
    >
      <ImprovedChatInterface userId={userId} />
    </RealtimeSessionEngineProvider>
  );
}

export default App;