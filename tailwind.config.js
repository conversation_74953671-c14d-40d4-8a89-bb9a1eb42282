/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      // <= 600px (Mobile First - Default)
      'sm': '0px', // Base styles apply here

      // 601px - 1024px (Tablet Portrait & Landscape)
      'md': '601px',

      // We ignore > 1024px as per requirements
    },
    extend: {
      // Add custom animations, colors, etc. if needed
      keyframes: {
        pulse: {
          '0%, 100%': { opacity: 1, transform: 'scale(1)' },
          '50%': { opacity: 0.7, transform: 'scale(1.05)' },
        }
      },
      animation: {
        pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      }
    },
  },
  plugins: [],
}
