import React from 'react';
import { useRealtimeSessionEngine } from 'gabber-client-react';
import VoiceVisualizer from './VoiceVisualizer';
// Import other necessary chat components (MessageList, InputArea - if applicable)

interface ChatInterfaceProps {
  userId: string | undefined; // Example prop
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ userId }) => {
  const {
    connectionState,
    canPlayAudio,
    startAudio,
    microphoneEnabled,
    setMicrophoneEnabled,
  } = useRealtimeSessionEngine(); // Expose audio and mic controls

  // Check connection state before rendering core UI
  const isConnected = connectionState === 'connected'; // Adjust based on actual SDK states

  return (
    // Full height container, centers content on md+, limits width on md+
    <div className="flex flex-col min-h-screen bg-white md:items-center md:justify-center">
      <div className="flex flex-col flex-grow w-full md:max-w-[600px] md:h-auto md:min-h-[80vh] md:border md:border-gray-200 md:rounded-lg md:shadow-sm overflow-hidden">
         {/* Header (Optional) */}
         <header className="p-4 border-b border-gray-200">
           <h1 className="text-lg font-semibold text-center">Voice Assistant</h1>
           {/* Display connection status or user info */}
           {/* <p className="text-xs text-gray-500 text-center">Status: {connectionState}</p> */}
           <p className="text-xs text-gray-500 text-center">User ID: {userId}</p>
         </header>

         {/* Message Area (Scrollable) */}
         <main className="flex-grow p-4 overflow-y-auto">
           {/* Render chat messages here based on `messages` from SDK */}
           {/* Placeholder: */}
           <p className="text-gray-500">Chat history will appear here...</p>
           {/* Add MessageList component */}
         </main>

         {/* Footer / Control Area */}
         <footer className="p-4 border-t border-gray-200 flex flex-col items-center">
           {!isConnected ? (
             <p className="text-sm text-gray-500">Connecting...</p>
           ) : !canPlayAudio ? (
             <button
               className="px-4 py-2 bg-blue-500 text-white rounded"
               onClick={async () => { await startAudio(); }}
             >
               Enable Audio
             </button>
           ) : (
             <>
               <VoiceVisualizer />
               <button
                 className={`mt-4 px-4 py-2 rounded ${
                   microphoneEnabled ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
                 }`}
                 onClick={async () => { await setMicrophoneEnabled(!microphoneEnabled); }}
               >
                 {microphoneEnabled ? 'Mute Mic' : 'Unmute Mic'}
               </button>
             </>
           )}
           {/* Add Mic Toggle Button, other controls */}
           {/* Example based on SDK sample: */}
           {/* <MicToggleButton /> */}
         </footer>
      </div>
    </div>
  );
};

export default ChatInterface;

// Example Mic Toggle (can be its own component)
// const MicToggleButton = () => {
//   const { microphoneEnabled, setMicrophoneEnabled, canPlayAudio, startAudio } = useRealtimeSessionEngine();
//
//   const handleToggle = async () => {
//     if (!canPlayAudio) {
//       await startAudio(); // Request user interaction for audio if needed
//     }
//     await setMicrophoneEnabled(!microphoneEnabled);
//   };
//
//   return (
//     <button
//       onClick={handleToggle}
//       className={`mt-4 px-4 py-2 rounded ${microphoneEnabled ? 'bg-red-500 text-white' : 'bg-green-500 text-white'}`}
//     >
//       {microphoneEnabled ? 'Mute Mic' : 'Unmute Mic'}
//     </button>
//   );
// };