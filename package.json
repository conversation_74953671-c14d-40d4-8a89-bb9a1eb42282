{"name": "voice-chat-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"gabber-client-core": "^0.21.0", "gabber-client-react": "^0.23.0", "js-cookie": "^3.0.5", "lucide-react": "^0.507.0", "ogl": "^1.0.11", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "cypress": "^14.3.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jest": "^29.7.0", "ogl-types": "^0.0.102", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "ts-jest": "^29.3.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}