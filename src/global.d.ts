// Global type declarations
import React from 'react';

declare module 'lucide-react';

declare module 'gabber-client-react' {
  // Add userAudioLevel to session context
  export interface RealtimeSessionEngineContextData {
    /** Audio level of the local user, 0–1 */
    userAudioLevel?: number;
  }

  /**
   * Hook to access real-time session engine context.
   */
  export function useRealtimeSessionEngine(): {
    /** Agent's current state */
    agentState: string;
    /** Agent's audio volume (0–1) */
    agentVolume?: number;
    /** Connection state */
    connectionState: string;
    /** Whether audio playback is available */
    canPlayAudio: boolean;
    /** Starts audio playback */
    startAudio: () => Promise<void>;
    /** Enables or disables the microphone */
    setMicrophoneEnabled: (enabled: boolean) => Promise<void>;
    /** Audio level of the local user, 0–1 */
    userAudioLevel?: number;
    /** Whether the microphone is enabled */
    microphoneEnabled: boolean;
  };

  export const RealtimeSessionEngineProvider: React.FC<{ connectionOpts: any; children?: React.ReactNode }>;
}
