import type { RealtimeSessionConnectionDetails } from 'gabber-client-core'; // Assuming this type exists
 // Assuming this type exists

const API_BASE_URL = 'http://0.0.0.0:8000'; // Or from env var

export interface StartSessionResponse extends RealtimeSessionConnectionDetails {
  // Add any other properties returned by your endpoint alongside connection details
  sessionId: string; // Example
}

export const startSimpleSession = async (
  serviceToken: string,
  personaId: string = "1b40915f-6941-4724-8a82-5dbca583a3f2" // Default or configurable
): Promise<StartSessionResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/voiceai/start_simple_session`, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        persona: personaId,
        serviceToken: serviceToken, // Pass the token from the cookie
      }),
    });

    if (!response.ok) {
      // Handle non-2xx responses specifically if needed (e.g., 401, 403)
      console.error(`Session start failed: ${response.status} ${response.statusText}`);
      const errorBody = await response.text(); // Get more error details if available
      console.error("Error details:", errorBody);
      throw new Error(`Session validation failed with status ${response.status}`);
    }

    const data: StartSessionResponse = await response.json();
    // IMPORTANT: Ensure the response structure matches RealtimeSessionConnectionDetails
    // If not, you might need to adapt the data structure here.
    return data;

  } catch (error) {
    console.error('Error starting session:', error);
    // Re-throw or handle specific network errors etc.
    throw error; // Propagate error for UI handling
  }
};