import React, { useEffect, useRef, useState } from 'react';
import { useRealtimeSessionEngine } from 'gabber-client-react';
import { Mic, Settings, X } from 'lucide-react';
import OGLOrb from './OGLOrb';
import LoadingOrb from './LoadingOrb';

const STREAM_TIMEOUT = 10000; // 10 seconds

const ImprovedChatInterface: React.FC = () => {
  const {
    connectionState,
    canPlayAudio,
    startAudio,
    setMicrophoneEnabled,
    userAudioLevel,
    agentState,
    messages
  } = useRealtimeSessionEngine() as any; // Using any temporarily until proper types are available

  const [isUserSpeaking, setIsUserSpeaking] = useState(false);
  const isAgentSpeaking = agentState === 'speaking';

  // Streaming response state
  const [streamingText, setStreamingText] = useState('');
  const streamTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentSessionIdRef = useRef<string | null>(null);
  
  // Track user speaking based on user audio level
  useEffect(() => {
    if (typeof userAudioLevel === 'number') {
      const newIsUserSpeaking = userAudioLevel > 0.1;
      setIsUserSpeaking(newIsUserSpeaking);
      
      // Clear the streaming text when user starts speaking
      if (newIsUserSpeaking && !isUserSpeaking) {
        setStreamingText('');
        if (streamTimeoutRef.current) {
          clearTimeout(streamTimeoutRef.current);
        }
      }
    }
  }, [userAudioLevel, isUserSpeaking]);

  // Auto-start microphone as soon as connection is established
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        if (!canPlayAudio) {
          await startAudio();
        }
        await setMicrophoneEnabled(true);
      } catch {
        // Handle error
      }
    };
    if (connectionState === 'connected') {
      initializeAudio();
    }
  }, [connectionState, canPlayAudio, startAudio, setMicrophoneEnabled]);

  // Handle real agent messages
  useEffect(() => {
    if (!messages || !messages.length) return;
    
    // Find all messages for the current session or discover a new session
    let latestAgentMessage = null;
    let sessionId = null;
    
    // Process messages to find the latest agent message and session
    for (let i = messages.length - 1; i >= 0; i--) {
      const msg = messages[i];
      
      // Skip non-agent messages
      if (!msg.agent) continue;
      
      // Extract session ID from message ID (format: "5_agent")
      const msgSessionId = msg.id.split('_')[0];
      
      // If we don't have a session ID yet, use this one
      if (!sessionId) {
        sessionId = msgSessionId;
        latestAgentMessage = msg;
      }
      
      // If this message is from a different session, stop processing
      if (msgSessionId !== sessionId) break;
    }
    
    // If no agent message found, do nothing
    if (!latestAgentMessage) return;
    
    // Check if this is a new session
    const isNewSession = currentSessionIdRef.current !== sessionId;
    
    // If it's a new session, clear the text
    if (isNewSession) {
      setStreamingText('');
      currentSessionIdRef.current = sessionId;
    }
    
    // Update with the latest text
    setStreamingText(latestAgentMessage.text);
    
    // Handle timeout for final messages
    if (latestAgentMessage.final) {
      if (streamTimeoutRef.current) clearTimeout(streamTimeoutRef.current);
      streamTimeoutRef.current = setTimeout(() => {
        setStreamingText('');
      }, STREAM_TIMEOUT);
    }
  }, [messages]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (streamTimeoutRef.current) clearTimeout(streamTimeoutRef.current);
    };
  }, []);

  // Loading state
  if (connectionState !== 'connected') {
    return <LoadingOrb />;
  }

  return (
    <div className="app-container" style={{ position: 'relative', width: '100vw', height: '100vh', overflow: 'hidden' }}>
      {/* Settings button - top right */}
      <button
        className="settings-button"
        style={{ position: 'absolute', top: 24, right: 24, zIndex: 20 }}
        aria-label="Settings"
      >
        <Settings style={{ width: 28, height: 28, color: 'white' }} />
      </button>

      {/* Close button - bottom left */}
      <button
        className="close-button"
        style={{ position: 'absolute', bottom: 32, left: 24, zIndex: 20, background: '#262626', borderRadius: '50%', padding: 12, border: 'none', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        aria-label="Close"
        onClick={() => window.location.reload()}
      >
        <X style={{ width: 28, height: 28, color: 'white' }} />
      </button>

      {/* Mic button - bottom right */}
      <button
        className="mic-button"
        style={{ position: 'absolute', bottom: 32, right: 24, zIndex: 20, background: '#262626', borderRadius: '50%', padding: 12, border: 'none', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        aria-label="Mic"
      >
        <Mic style={{ width: 28, height: 28, color: 'white' }} />
        {isUserSpeaking && (
          <span className="user-speaking-indicator" style={{ position: 'absolute', top: 6, right: 6, width: 12, height: 12, background: '#22c55e', borderRadius: '50%' }}></span>
        )}
      </button>

      {/* Streaming response text */}
      {streamingText && (
        <div
          className="streaming-response"
          style={{
            position: 'absolute',
            bottom: '15%',
            left: '50%',
            transform: 'translateX(-50%)',
            color: 'white',
            fontSize: 12,
            fontWeight: 400,
            background: 'rgba(0,0,0,0.6)',
            borderRadius: 16,
            padding: '16px 32px',
            zIndex: 15,
            maxWidth: '90vw',
            textAlign: 'center',
            boxShadow: '0 2px 16px rgba(0,0,0,0.2)'
          }}
        >
          {streamingText}
        </div>
      )}

      {/* Orb center */}
      <div className="orb-wrapper" style={{ 
        width: '100%', 
        height: 600, 
        position: 'relative',
        zIndex: 10,
        overflow: 'visible' // Ensure the orb isn't clipped by the container
      }}>
        <OGLOrb
          hue={0}
          hoverIntensity={0.5}
          rotateOnHover={true}
          forceHoverState={isAgentSpeaking}
        />
      </div>
    </div>
  );
};

export default ImprovedChatInterface;