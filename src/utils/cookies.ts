
export const getAuthCookies = (): { userId: string | undefined; serviceToken: string | undefined } => {
  // NOTE: Flutter InAppWebView needs to set these as *accessible* to JS,
  // meaning HttpOnly=false. This has security implications if the WebView
  // can navigate to arbitrary sites. Ensure the WebView is locked down.
  const userId = "riteshr24";//Cookies.get('userId');
  const serviceToken = "240567";//Cookies.get('serviceToken');
  return { userId, serviceToken };
}