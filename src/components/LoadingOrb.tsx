import React from 'react';

const LoadingOrb: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black">
      <div className="relative">
        {/* Outer glow animation */}
        <div 
          className="absolute rounded-full animate-pulse"
          style={{
            width: '220px',
            height: '220px',
            filter: 'blur(20px)',
            opacity: '0.6',
            background: 'radial-gradient(circle, rgba(96,165,250,0.7) 0%, rgba(59,130,246,0.3) 60%, rgba(37,99,235,0) 100%)',
            animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            top: '-10px',
            left: '-10px'
          }}
        />
        
        {/* Main orb */}
        <div 
          className="rounded-full"
          style={{
            width: '200px',
            height: '200px',
            background: 'radial-gradient(circle, #3b82f6 10%, #60a5fa 60%, #93c5fd 100%)',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Inner core */}
          <div
            className="absolute rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            style={{
              width: '120px',
              height: '120px',
              background: 'radial-gradient(circle, #1e40af 0%, #3b82f6 100%)'
            }}
          />
          
          {/* Loading animation (inner spinning light) */}
          <div 
            className="absolute top-0 left-0 w-full h-full animate-spin"
            style={{ 
              animation: 'spin 3s linear infinite',
              background: 'conic-gradient(from 0deg at 50% 50%, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0) 100%)'
            }}
          />
        </div>
      </div>
      
      {/* Loading text */}
      <div className="mt-8 text-white text-lg font-light">
        Connecting...
      </div>
    </div>
  );
};

export default LoadingOrb;